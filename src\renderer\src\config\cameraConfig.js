/**
 * Camera 组件配置文件
 * 包含所有摄像头相关的配置常量和设置
 */

// 摄像头配置常量
export const CAMERA_CONFIG = {
  // 分辨率配置
  RESOLUTION: {
    DOCUMENT: { width: { ideal: 1920 }, height: { ideal: 1080 } },
    PORTRAIT: { width: { ideal: 1280 }, height: { ideal: 720 } },
    IDCARD: { width: { ideal: 1280 }, height: { ideal: 720 } },
    FALLBACK: { width: { ideal: 640 }, height: { ideal: 480 } }
  },

  // 帧率配置
  FRAME_RATE: { ideal: 24 },

  // 图片质量配置
  IMAGE_QUALITY: {
    DOCUMENT: 0.85,
    PORTRAIT: 0.9,
    IDCARD: 0.9,
    PREVIEW: 0.7
  },

  // 时间配置
  TIMING: {
    COUNTDOWN_DURATION: 3,
    AUTO_SWITCH_DELAY: 1500,
    RETRY_DELAY: 1000,
    PROCESSING_TIMEOUT: 10000,
    TOAST_DURATION: 3000
  },

  // 裁剪框配置
  CROP_STYLES: {
    'idcard-front': {
      width: '60%', // 身份证比例约为 1.6:1，适合横向拍摄
      height: '38%',
      position: 'absolute',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)'
    },
    'idcard-back': {
      width: '60%', // 身份证背面同样比例
      height: '38%',
      position: 'absolute',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)'
    },
    portrait: {
      width: '50%', // 证件照比例约为 0.75:1
      height: '90%',
      position: 'absolute',
      top: '50%',
      left: '50%',
      transform: 'translate(-50%, -50%)'
    }
  },

  // 拍照模式顺序
  SHOOTING_MODE_ORDER: ['document', 'idcard', 'portrait'],

  // 设备选择规则
  DEVICE_SELECTION: {
    PORTRAIT_KEYWORDS: ['USB'],
    DOCUMENT_KEYWORDS: ['ZJCX']
  },

  // 目标图片尺寸
  TARGET_IMAGE_SIZE: {
    DOCUMENT: 1280,
    PORTRAIT: 1280,
    IDCARD: 1280
  }
}

// 错误类型枚举
export const CAMERA_ERRORS = {
  PERMISSION_DENIED: 'NotAllowedError',
  DEVICE_NOT_FOUND: 'NotFoundError',
  DEVICE_IN_USE: 'NotReadableError',
  OVERCONSTRAINED: 'OverconstrainedError',
  BROWSER_NOT_SUPPORTED: 'BrowserNotSupported',
  VIDEO_NOT_READY: 'VideoNotReady',
  CANVAS_ERROR: 'CanvasError'
}

// 错误消息映射
export const ERROR_MESSAGES = {
  [CAMERA_ERRORS.PERMISSION_DENIED]: '摄像头权限被拒绝，请在浏览器设置中允许访问摄像头',
  [CAMERA_ERRORS.DEVICE_NOT_FOUND]: '未找到摄像头设备，请检查设备连接',
  [CAMERA_ERRORS.DEVICE_IN_USE]: '摄像头正在被其他应用使用，请关闭其他应用后重试',
  [CAMERA_ERRORS.OVERCONSTRAINED]: '摄像头不支持请求的配置，正在尝试降级设置',
  [CAMERA_ERRORS.BROWSER_NOT_SUPPORTED]: '当前浏览器不支持摄像头功能，请使用现代浏览器',
  [CAMERA_ERRORS.VIDEO_NOT_READY]: '视频流未准备就绪，请稍后重试',
  [CAMERA_ERRORS.CANVAS_ERROR]: '图片处理失败，请重新拍摄'
}

// 拍照状态初始值
export const INITIAL_PHOTOS_TAKEN = {
  document: false,
  'idcard-front': false,
  'idcard-back': false,
  portrait: false
}

// 身份证模式
export const ID_CARD_MODES = {
  FRONT: 'front',
  BACK: 'back'
}

// 拍照模式
export const PHOTO_MODES = {
  DOCUMENT: 'document',
  IDCARD: 'idcard',
  PORTRAIT: 'portrait'
}

// 样式常量
export const STYLES = {
  VIDEO_CONTAINER: {
    position: 'relative',
    overflow: 'hidden',
    borderRadius: '0.75rem',
    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    border: '4px solid #14b8a6'
  },
  VIDEO: {
    width: '100%',
    height: '100%',
    objectFit: 'contain'
  }
}

// 工具函数
export const CAMERA_UTILS = {
  /**
   * 获取下一个拍摄模式（按固定顺序）
   * @param {string} currentMode 当前模式
   * @returns {string|null} 下一个模式或null
   */
  getNextMode: (currentMode) => {
    const currentIndex = CAMERA_CONFIG.SHOOTING_MODE_ORDER.indexOf(currentMode)
    if (currentIndex < CAMERA_CONFIG.SHOOTING_MODE_ORDER.length - 1) {
      return CAMERA_CONFIG.SHOOTING_MODE_ORDER[currentIndex + 1]
    }
    return null
  },

  /**
   * 获取下一个未完成的拍摄模式
   * @param {Object} photosTaken 拍照状态对象
   * @returns {string|null} 下一个未完成的模式或null
   */
  getNextIncompleteMode: (photosTaken) => {
    // 检查文档模式
    if (!photosTaken.document) {
      return PHOTO_MODES.DOCUMENT
    }

    // 检查身份证模式（需要正反面都完成）
    if (!photosTaken['idcard-front'] || !photosTaken['idcard-back']) {
      return PHOTO_MODES.IDCARD
    }

    // 检查人像模式
    if (!photosTaken.portrait) {
      return PHOTO_MODES.PORTRAIT
    }

    // 所有模式都已完成
    return null
  },

  /**
   * 生成日期目录格式
   * @returns {string} 格式化的日期字符串
   */
  generateDateDir: () => {
    const today = new Date()
    return `${today.getFullYear()}${String(today.getMonth() + 1).padStart(2, '0')}${String(today.getDate()).padStart(2, '0')}`
  },

  /**
   * 生成文件名
   * @param {string} mode 拍照模式
   * @param {string} idCardMode 身份证模式（可选）
   * @returns {string} 文件名
   */
  generateFileName: (mode, idCardMode = null) => {
    const dateDir = CAMERA_UTILS.generateDateDir()
    const photoType = mode === PHOTO_MODES.IDCARD ? `idcard-${idCardMode}` : mode
    return `${dateDir}/photo_${photoType}_${Date.now()}.jpg`
  },

  /**
   * 获取拍照键值
   * @param {string} mode 拍照模式
   * @param {string} idCardMode 身份证模式（可选）
   * @returns {string} 拍照键值
   */
  getPhotoKey: (mode, idCardMode = null) => {
    return mode === PHOTO_MODES.IDCARD ? `idcard-${idCardMode}` : mode
  },

  /**
   * 检查是否所有照片都已拍摄
   * @param {Object} photosTaken 拍照状态对象
   * @returns {boolean} 是否全部完成
   */
  areAllPhotosTaken: (photosTaken) => {
    return Object.values(photosTaken).every((taken) => taken)
  }
}
