import { useEffect, useCallback } from 'react'
import { useToast } from '../contexts/ToastContext'
import { useCameraState, useCountdown, useAutoModeSwitch } from '../hooks/useCameraState'
import { useCameraActions } from '../hooks/useCameraActions'
import { CAMERA_UTILS, PHOTO_MODES } from '../config/cameraConfig'

// 子组件导入
import ProcessingOverlay from './Camera/ProcessingOverlay'
import PhotoProgress from './Camera/PhotoProgress'
import PhotoPreview from './Camera/PhotoPreview'
import CameraView from './Camera/CameraView'

function Camera({ onClose, onCapture, onPhotoComplete, menuLabel }) {
  const { showToast } = useToast()

  // 使用自定义状态管理 Hook
  const { state, dispatch, actions } = useCameraState()

  // 使用摄像头操作 Hook
  const cameraActions = useCameraActions(state, dispatch, actions)

  // 使用自动模式切换 Hook
  const { startAutoSwitch, cancelAutoSwitch } = useAutoModeSwitch(
    state,
    dispatch,
    cameraActions.setupCamera
  )

  // 使用倒计时 Hook
  useCountdown(state, dispatch, cameraActions.takePhoto)

  // 处理保存照片
  const handleSave = useCallback(() => {
    if (state.previewImage) {
      onCapture(state.previewImage)

      // 更新拍照状态
      const photoKey = CAMERA_UTILS.getPhotoKey(state.mode, state.idCardMode)
      dispatch(actions.markPhotoTaken(photoKey))

      // 检查是否所有照片都已完成
      const updatedPhotosTaken = {
        ...state.photosTaken,
        [photoKey]: true
      }
      const allCompleted = CAMERA_UTILS.areAllPhotosTaken(updatedPhotosTaken)

      // 如果所有照片都已完成，立即触发完成回调
      if (allCompleted) {
        showToast('所有照片拍摄完成！', 'success')
        // 延迟一下让用户看到成功提示，然后触发完成回调
        setTimeout(() => {
          onPhotoComplete()
        }, 1500)
      } else {
        // 身份证拍照完成后的逻辑
        if (state.mode === PHOTO_MODES.IDCARD) {
          if (state.idCardMode === 'front') {
            // 拍完正面，自动切换到反面
            showToast('身份证正面拍照完成', 'success')
            setTimeout(() => {
              dispatch(actions.setIdCardMode('back'))
            }, 1500)
          } else {
            // 拍完反面，检查是否需要跳转到下一个模式
            showToast('身份证反面拍照完成', 'success')
            // 使用智能模式切换，找到下一个未完成的模式
            const nextIncompleteMode = CAMERA_UTILS.getNextIncompleteMode(updatedPhotosTaken)
            if (nextIncompleteMode) {
              startAutoSwitch(nextIncompleteMode)
            }
          }
        } else {
          // 其他模式拍照完成后的逻辑
          showToast('照片保存成功', 'success')
          // 使用智能模式切换，找到下一个未完成的模式
          const nextIncompleteMode = CAMERA_UTILS.getNextIncompleteMode(updatedPhotosTaken)
          if (nextIncompleteMode) {
            startAutoSwitch(nextIncompleteMode)
          }
        }
      }

      // 关闭预览
      dispatch(actions.hidePreview())
      dispatch(actions.resetCountdown())
    }
  }, [
    state.previewImage,
    state.mode,
    state.idCardMode,
    state.photosTaken,
    dispatch,
    actions,
    onCapture,
    onPhotoComplete,
    showToast,
    startAutoSwitch
  ])

  // 键盘快捷键处理函数
  const handleKeyDown = useCallback(
    (event) => {
      // 防止在输入框等元素中触发快捷键
      if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
        return
      }

      // 防止默认行为和事件冒泡
      event.preventDefault()
      event.stopPropagation()

      switch (event.key) {
        case 'Enter':
        case ' ': // 空格键
          // 开始拍摄快捷键
          if (!state.showPreview && !state.isCountingDown && state.canStartCapture) {
            cameraActions.startCapture()
          } else if (state.showPreview && state.previewImage) {
            // 在预览模式下，Enter 和空格键用于保存照片
            handleSave()
          }
          break
        case 'Escape':
          // 取消拍摄快捷键
          if (state.isCountingDown) {
            // 如果正在倒计时，停止倒计时
            dispatch(actions.resetCountdown())
          } else if (state.showPreview) {
            // 如果在预览模式，重新拍摄
            cameraActions.handleRetake()
          } else {
            // 否则关闭相机
            onClose()
          }
          break
        default:
          break
      }
    },
    [
      state.showPreview,
      state.isCountingDown,
      state.canStartCapture,
      state.previewImage,
      cameraActions.startCapture,
      cameraActions.handleRetake,
      handleSave,
      dispatch,
      actions,
      onClose
    ]
  )

  // 键盘事件监听器
  useEffect(() => {
    // 添加键盘事件监听器
    document.addEventListener('keydown', handleKeyDown)

    // 清理函数
    return () => {
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [handleKeyDown])

  // 初始化摄像头 - 只在组件挂载时执行一次
  useEffect(() => {
    cameraActions.setupCamera(null, state.mode)
    return () => {
      // 组件卸载时清理资源
      dispatch(actions.resetState())
    }
  }, []) // 空依赖数组，只在挂载时执行

  // 监听预览状态变化 - 移除setupCamera依赖避免循环
  useEffect(() => {
    if (!state.showPreview && state.selectedCamera && !state.stream) {
      // 当退出预览模式且没有视频流时，重新初始化摄像头
      // 使用setTimeout避免立即执行导致的状态冲突
      const timer = setTimeout(() => {
        cameraActions.setupCamera(state.selectedCamera, state.mode)
      }, 200)

      return () => clearTimeout(timer)
    }
  }, [state.showPreview, state.stream]) // 监听showPreview和stream变化

  // 监听身份证模式变化 - 当身份证正反面切换时重新初始化摄像头
  useEffect(() => {
    if (state.mode === PHOTO_MODES.IDCARD && !state.showPreview && state.selectedCamera) {
      console.log(`身份证模式切换到: ${state.idCardMode}`)
      // 身份证正反面切换时，强制重新初始化摄像头以确保正确的配置
      const timer = setTimeout(() => {
        console.log('强制重新初始化摄像头 - 身份证模式切换')
        cameraActions.setupCamera(state.selectedCamera, state.mode, true) // 第三个参数为true表示强制重新初始化
      }, 500) // 增加延迟确保状态完全稳定

      return () => clearTimeout(timer)
    }
  }, [state.idCardMode, state.showPreview]) // 监听idCardMode和showPreview变化

  return (
    <div className="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50">
      {/* 处理状态遮罩 */}
      <ProcessingOverlay visible={state.processing} />

      <div className="bg-white rounded-2xl p-4 w-full relative">
        {/* 拍照进度指示器 */}
        <PhotoProgress
          photosTaken={state.photosTaken}
          allPhotosTaken={state.allPhotosTaken}
          onComplete={onPhotoComplete}
          menuLabel={menuLabel}
        />

        {state.showPreview ? (
          <PhotoPreview
            previewImage={state.previewImage}
            mode={state.mode}
            idCardMode={state.idCardMode}
            onSave={handleSave}
            onRetake={cameraActions.handleRetake}
          />
        ) : (
          <CameraView
            videoRef={cameraActions.videoRef}
            mode={state.mode}
            idCardMode={state.idCardMode}
            isCountingDown={state.isCountingDown}
            countdown={state.countdown}
            onModeChange={cameraActions.setMode}
            onIdCardModeChange={cameraActions.setIdCardMode}
            onStartCapture={cameraActions.startCapture}
            onClose={onClose}
            onCancelAutoSwitch={cancelAutoSwitch}
          />
        )}
      </div>
    </div>
  )
}

export default Camera
